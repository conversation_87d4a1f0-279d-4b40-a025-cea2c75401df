<script lang="ts" setup>
import { getSignalStrengthPercentage, rssiToPercentage } from '@layouts/utils'
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

onMounted(() => {
  getClientInfo()
})

interface Terminal {
  ip: string
  name: string
  ssid: string
  access_way: string
  rssi: number
  user_name: string
  expiry_human: string
  download: string
  upload: string
  mac: string
  connected_device?: string // 连接设备信息
}

const terminalList = ref<Terminal[]>([])
const loading = ref(false)
const itemsPerPage = ref(10)
const page = ref(1)

const totalItems = computed(() => {
  if (networkType.value !== 'all')
    return terminalList.value.filter(item => item.access_way === networkType.value).length || 0

  return terminalList.value.length
})

// 排序相关变量
const sortBy = ref<{ key: string; order?: boolean }[]>([])

const getOrder = (order: any) => (order ? 1 : -1)

const list = computed((): Terminal[] => {
  let filtered = terminalList.value
  if (networkType.value !== 'all')
    filtered = filtered.filter(item => item.access_way === networkType.value)

  // 排序逻辑
  if (sortBy.value.length > 0) {
    const { key, order } = sortBy.value[0]

    filtered = [...filtered].sort((a, b) => {
      let aValue, bValue

      if (key === 'rssi') {
        const rssiValue = (val: any) => {
          if (typeof val.rssi === 'number')
            return val.rssi
          if (val.rssi === '1G')
            return 1000
          if (val.rssi === '2.5G')
            return 2500
          if (val.rssi === '100')
            return 100

          return -1000
        }

        aValue = rssiValue(a)
        bValue = rssiValue(b)
      }
      else if (key === 'traffic') {
        const parseTraffic = (val: string) => {
          if (!val)
            return 0
          const num = Number.parseFloat(val)
          if (val.includes('GB'))
            return num * 1024 * 1024 * 1024
          if (val.includes('MB'))
            return num * 1024 * 1024
          if (val.includes('KB'))
            return num * 1024
          if (val.includes('B'))
            return num

          return num
        }

        aValue = parseTraffic(a.download) + parseTraffic(a.upload)
        bValue = parseTraffic(b.download) + parseTraffic(b.upload)
      }
      else {
        aValue = (a as any)[key]
        bValue = (b as any)[key]
      }

      if (aValue < bValue)
        return order === 'asc' ? -1 : 1
      if (aValue > bValue)
        return order === 'asc' ? 1 : -1

      return 0
    })
  }

  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return filtered.slice(start, end)
})

// 表头定义
const headers = [
  { title: t('Name'), key: 'name', sortable: false },
  { title: t('IPAddress'), key: 'ip', sortable: false },
  { title: 'MAC', key: 'mac', sortable: false },
  { title: t('AccessType'), key: 'access_way', sortable: false },
  { title: t('SignalStrength'), key: 'rssi', sortable: true },
  { title: t('ConnectedDevice'), key: 'connected_device', sortable: false },
  { title: 'SSID', key: 'ssid', sortable: false },
  { title: t('LeaseRemaining'), key: 'expiry_human', sortable: false },
  { title: t('HistoryTraffic'), key: 'traffic', sortable: true },
]

const getClientInfo = () => {
  loading.value = true
  $post('', {
    requestType: 536,
  }).then(res => {
    if (res.msg === 'success') {
      const clientList = res.info.client_list.filter((item: Terminal) => item.mac) || []

      // 为每个客户端添加连接设备信息
      terminalList.value = clientList.map((item: Terminal) => ({
        ...item,
        connected_device: getConnectedDeviceInfo(item),
      }))
    }
  }).finally(() => {
    loading.value = false
  })
}

// 根据连接方式获取连接设备信息
const getConnectedDeviceInfo = (terminal: Terminal): string => {
  if (terminal.access_way === 'LAN') {
    // 有线连接，显示路由器
    return t('Router')
  }
  else if (terminal.access_way === '2.4GHz' || terminal.access_way === '5GHz') {
    // 无线连接，显示AP
    return t('AP')
  }
  else {
    // 其他连接方式
    return '--'
  }
}

const networkType = ref('all')

const networkTypeOptions = ref([
  { title: t('All'), value: 'all' },
  { title: 'LAN', value: 'LAN' },
  { title: '2.4G', value: '2.4GHz' },
  { title: '5G', value: '5GHz' },
])

const sortchange = (val: any) => {
  sortBy.value = val
}
</script>

<template>
  <VCard>
    <template #title>
      <div class="d-flex align-center justify-space-between">
        <div class="text-h5">
          {{ t('OnlineClients') }}
        </div>
        <BtnGroupSelector
          v-model:value="networkType"
          :options="networkTypeOptions"
          class="mr-6"
          item-title="title"
          item-value="value"
        />
      </div>
    </template>
    <VCardText class="pa-0">
      <VDivider />
      <VDataTableServer
        :headers="headers"
        :items="list"
        :items-length="totalItems"
        :loading="loading"
        item-value="mac"
        :no-data-text="t('NoData')"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <template #item.rssi="{ item }">
          <div style="display: flex;align-items: center;">
            <template v-if="item.access_way === 'LAN'">
              <img
                v-if="item.rssi == '1G'"
                alt=""
                src="../../assets/images/lan/1000.svg"
              >
              <img
                v-else-if="item.rssi == '2.5G'"
                alt=""
                src="../../assets/images/lan/2500.svg"
              >
              <img
                v-else
                alt=""
                src="../../assets/images/lan/100.svg"
              >
            </template>
            <template v-else>
              <img
                v-if="rssiToPercentage(item.rssi) == 1"
                alt=""
                src="../../assets/images/lan/wifi1.svg"
              >
              <img
                v-if="rssiToPercentage(item.rssi) == 2"
                alt=""
                src="../../assets/images/lan/wifi2.svg"
              >
              <img
                v-if="rssiToPercentage(item.rssi) == 3"
                alt=""
                src="../../assets/images/lan/wifi3.svg"
              >
              <img
                v-if="rssiToPercentage(item.rssi) == 4"
                alt=""
                src="../../assets/images/lan/wifi4.svg"
              >
            </template>
            <template v-if="item.access_way === 'LAN'">
              <div
                v-if="item.rssi == '1G'"
                class="text-7"
              >
                {{ getSignalStrengthPercentage(item.rssi) || '--' }}
              </div>
              <div
                v-else-if="item.rssi == '2.5G'"
                class="text-6"
              >
                {{ getSignalStrengthPercentage(item.rssi) || '--' }}
              </div>
              <div
                v-else
                class="text-5"
              >
                {{ getSignalStrengthPercentage(item.rssi) || '--' }}
              </div>
            </template>
            <template v-else>
              <div
                v-if="rssiToPercentage(item.rssi) == 4"
                class="text-4"
              >
                {{ `${getSignalStrengthPercentage(item.rssi)}%` || '0%' }}
              </div>
              <div
                v-if="rssiToPercentage(item.rssi) == 3"
                class="text-3"
              >
                {{ `${getSignalStrengthPercentage(item.rssi)}%` || '0%' }}
              </div>
              <div
                v-if="rssiToPercentage(item.rssi) == 2"
                class="text-2"
              >
                {{ `${getSignalStrengthPercentage(item.rssi)}%` || '0%' }}
              </div>
              <div
                v-if="rssiToPercentage(item.rssi) == 1"
                class="text-1"
              >
                {{ `${getSignalStrengthPercentage(item.rssi)}%` || '0%' }}
              </div>
            </template>
          </div>
        </template>
        <template #item.connected_device="{ item }">
          {{ item.connected_device || '--' }}
        </template>

        <template #item.ssid="{ item }">
          {{ item.ssid || '--' }}
        </template>

        <template #item.expiry_human="{ item }">
          {{ item.expiry_human || '--' }}
        </template>

        <template #item.traffic="{ item }">
          <div class="text-high-emphasis">
            <div class="download">
              ↓ {{ item.download }}
            </div>
            <div class="upload">
              ↑ {{ item.upload }}
            </div>
          </div>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalItems"
          />
        </template>
      </VDataTableServer>
    </VCardText>
  </VCard>
</template>

<style lang="scss">
.download {
  color: rgba($color: var(--v-theme-primary), $alpha: 100%);
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 18px;
}

.upload {
  color: rgba($color: var(--v-theme-success), $alpha: 100%);
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 18px;
}

.text-1 {
  color: var(--Color-Error-error-400, #ff7074);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
}

.text-2 {
  color: var(--Color-Error-error-400, #ff7074);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
}

.text-3 {
  color: var(--Color-Warning-warning-400, #ffb269);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
}

.text-4 {
  color: var(--Color-Success-success-500, #28c76f);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
}

.text-5 {
  color: var(--Color-Warning-warning-500, #ff9f43);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
}

.text-6 {
  color: var(--Color-Primary-primary-700, #0e42d2);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
}

.text-7 {
  color: var(--Color-Primary-primary-500, #4080ff);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
}
</style>
